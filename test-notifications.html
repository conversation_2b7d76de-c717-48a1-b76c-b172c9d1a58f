<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .notification-log {
            background: #f5f5f5;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
        }
        .notification-item {
            margin: 5px 0;
            padding: 5px;
            background: white;
            border-left: 3px solid #007bff;
        }
        .user-input { border-left-color: #28a745; }
        .ai-response { border-left-color: #dc3545; }
        .status { border-left-color: #ffc107; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .controls {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>WebSDK 通知功能测试</h1>
    
    <div class="controls">
        <h3>控制面板</h3>
        <button onclick="initializeSDK()">初始化SDK</button>
        <button onclick="connectWebSocket()">连接WebSocket</button>
        <button onclick="setupNotificationListeners()">设置通知监听器</button>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <div class="controls">
        <h3>测试功能</h3>
        <button onclick="testUserInput()">模拟用户输入</button>
        <button onclick="testAIResponse()">模拟AI响应</button>
        <button onclick="testStatusNotification()">模拟状态通知</button>
    </div>

    <h3>通知日志</h3>
    <div id="notificationLog" class="notification-log">
        <div class="notification-item">等待初始化...</div>
    </div>

    <!-- 聊天组件容器 -->
    <div id="chat-container" style="margin-top: 20px;">
        <h3>聊天组件</h3>
        <chat-widget></chat-widget>
    </div>

    <script type="module">
        import { init, getWebSDK } from './dist/index.js';

        let sdk = null;
        let notificationLog = document.getElementById('notificationLog');

        function logNotification(type, method, params) {
            const timestamp = new Date().toLocaleTimeString();
            const item = document.createElement('div');
            item.className = `notification-item ${type}`;
            item.innerHTML = `
                <strong>[${timestamp}] ${method}</strong><br>
                <pre>${JSON.stringify(params, null, 2)}</pre>
            `;
            notificationLog.appendChild(item);
            notificationLog.scrollTop = notificationLog.scrollHeight;
        }

        window.initializeSDK = async function() {
            try {
                logNotification('status', 'SDK初始化', { status: '开始' });
                
                sdk = await init({
                    debug: true,
                    aiServerUrl: 'http://localhost:8001',
                    hksttUrl: 'ws://localhost:20096',
                    websocketUrl: 'ws://localhost:8080'
                });

                logNotification('status', 'SDK初始化', { status: '成功' });
            } catch (error) {
                logNotification('status', 'SDK初始化', { status: '失败', error: error.message });
            }
        };

        window.connectWebSocket = function() {
            if (!sdk) {
                logNotification('status', 'WebSocket连接', { status: '失败', reason: 'SDK未初始化' });
                return;
            }
            
            logNotification('status', 'WebSocket连接', { status: '尝试连接' });
            // WebSocket连接通常在SDK初始化时自动进行
        };

        window.setupNotificationListeners = function() {
            if (!sdk) {
                logNotification('status', '设置监听器', { status: '失败', reason: 'SDK未初始化' });
                return;
            }

            // 监听用户输入通知
            sdk.onNotification('notifications/userInput', (params) => {
                logNotification('user-input', 'notifications/userInput', params);
            });

            // 监听AI响应通知
            sdk.onNotification('notifications/aiResponse', (params) => {
                logNotification('ai-response', 'notifications/aiResponse', params);
            });

            // 监听状态通知
            sdk.onNotification('notifications/status', (params) => {
                logNotification('status', 'notifications/status', params);
            });

            // 监听新用户通知
            sdk.onNotification('notifications/newUser', (params) => {
                logNotification('status', 'notifications/newUser', params);
            });

            logNotification('status', '设置监听器', { status: '成功' });
        };

        window.testUserInput = function() {
            if (!sdk) {
                logNotification('status', '测试用户输入', { status: '失败', reason: 'SDK未初始化' });
                return;
            }

            // 模拟用户输入事件
            const eventBus = sdk.getEventBus();
            eventBus.emit('client:send-notification', {
                jsonrpc: '2.0',
                method: 'notifications/userInput',
                params: {
                    userInput: '测试用户输入消息',
                    requestId: 'test-req-' + Date.now(),
                    sessionId: 'test-session-123'
                }
            });

            logNotification('status', '测试用户输入', { status: '已发送' });
        };

        window.testAIResponse = function() {
            if (!sdk) {
                logNotification('status', '测试AI响应', { status: '失败', reason: 'SDK未初始化' });
                return;
            }

            // 模拟AI响应事件
            const eventBus = sdk.getEventBus();
            eventBus.emit('client:send-notification', {
                jsonrpc: '2.0',
                method: 'notifications/aiResponse',
                params: {
                    requestId: 'test-req-' + Date.now(),
                    message: '这是一个测试AI响应',
                    sessionId: 'test-session-123',
                    action: 'login',
                    data: { type: 'idCard' }
                }
            });

            logNotification('status', '测试AI响应', { status: '已发送' });
        };

        window.testStatusNotification = function() {
            if (!sdk) {
                logNotification('status', '测试状态通知', { status: '失败', reason: 'SDK未初始化' });
                return;
            }

            // 模拟状态通知事件
            const eventBus = sdk.getEventBus();
            eventBus.emit('client:send-notification', {
                jsonrpc: '2.0',
                method: 'notifications/status',
                params: {
                    requestId: 'test-req-' + Date.now(),
                    message: '正在处理您的请求...'
                }
            });

            logNotification('status', '测试状态通知', { status: '已发送' });
        };

        window.clearLog = function() {
            notificationLog.innerHTML = '<div class="notification-item">日志已清空</div>';
        };

        // 页面加载完成后自动初始化
        window.addEventListener('load', () => {
            logNotification('status', '页面加载', { status: '完成' });
        });
    </script>
</body>
</html>
