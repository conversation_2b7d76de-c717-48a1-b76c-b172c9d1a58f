/**
 * 对话窗口组件
 * 负责处理消息显示和用户输入
 */

import { Send, User, MessageCircle } from 'lucide-react';
import React, { useState, useEffect, useRef } from 'react';

import { Card, CardContent } from '../../components/ui/card';
import { Input } from '../../components/ui/input';
import { ScrollArea } from '../../components/ui/scroll-area';
import { WebSDK } from '../../core/WebSDK';
import { generateUUID } from '../../utils/helpers';
import { Logger } from '../../utils/Logger';

// 添加流式消息动画样式
const streamingStyles = `
  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.3; }
    100% { opacity: 1; }
  }

  .streaming-message {
    animation: pulse 1.5s ease-in-out infinite;
  }
`;

// 注入样式到页面
if (typeof document !== 'undefined') {
  const existingStyle = document.getElementById('chat-window-streaming-styles');
  if (!existingStyle) {
    const styleElement = document.createElement('style');
    styleElement.id = 'chat-window-streaming-styles';
    styleElement.textContent = streamingStyles;
    document.head.appendChild(styleElement);
  }
}

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  sessionId: string;
  status: 'pending' | 'sending' | 'sent' | 'delivered' | 'failed' | 'streaming';
  createdAt: number;
  updatedAt: number;
}

interface ChatWindowProps {
  sdk: WebSDK;
  sessionId: string;
  messages: Message[];
  isLoading: boolean;
  onMessagesChange: (messages: Message[]) => void;
  onLoadingChange: (loading: boolean) => void;
  className?: string;
  style?: React.CSSProperties;
  layoutMode?: 'standard' | 'care'; // 添加布局模式支持
}

export const ChatWindow: React.FC<ChatWindowProps> = ({
  sdk,
  sessionId,
  messages,
  isLoading,
  onMessagesChange,
  onLoadingChange,
  className,
  style,
  layoutMode = 'standard', // 默认为标准模式
}) => {
  const [inputValue, setInputValue] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const loggerRef = useRef(Logger.getInstance({ prefix: 'ChatWindow' }));

  // 添加消息
  const addMessage = (message: Message) => {
    onMessagesChange([...messages, message]);
    // 移除强制滚动，让useEffect处理
  };

  // 发送消息
  const sendMessage = (text: string) => {
    if (!text.trim() || !sdk || !sdk.getStatus().isReady) {
      loggerRef.current.warn('无法发送消息：文本为空或SDK未就绪');
      return;
    }

    const logger = loggerRef.current;
    const requestId = generateUUID();
    const eventBus = sdk.getEventBus();

    // 发送用户输入通知给SDK内部监听器
    eventBus.emit('jsonrpc:notification', {
      jsonrpc: '2.0',
      method: 'notifications/userInput',
      params: {
        userInput: text,
        requestId: requestId,
        sessionId: sessionId,
      },
    });

    // 添加用户消息到UI
    addMessage({
      id: `user-${Date.now()}`,
      role: 'user',
      content: text,
      sessionId: sessionId,
      status: 'delivered',
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    // 设置加载状态
    onLoadingChange(true);

    // 发送到AI处理
    eventBus.emit('ai:send-chat-request', {
      userInput: text,
      sessionId: sessionId,
      requestId: requestId,
    });

    logger.info('发送消息到AI', { text: text.substring(0, 50), sessionId });
  };

  // 处理发送按钮点击
  const handleSend = () => {
    const text = inputValue.trim();
    if (!text || isLoading) {
      return;
    }

    sendMessage(text);
    setInputValue('');
  };

  // 处理回车键
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  // 智能滚动逻辑：新消息时自动滚动到底部
  useEffect(() => {
    const container = messagesEndRef.current?.parentElement?.parentElement;
    if (!container || messages.length === 0) return;

    // 获取最新消息
    const latestMessage = messages[messages.length - 1];
    if (!latestMessage) return;

    // 如果是用户发送的消息，总是滚动到底部
    if (latestMessage.role === 'user') {
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 10);
      return;
    }

    // 如果是AI回复，检查用户是否在底部（允许100px的误差）
    const isAtBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;

    if (isAtBottom) {
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 10);
    }
  }, [messages]);

  return (
    <Card
      className={className}
      style={{
        flex: 1,
        backgroundColor: 'rgba(255, 255, 255, 0.8)', // 改为与其他组件一致的白色背景
        backdropFilter: 'blur(20px) saturate(180%)',
        border: '1px solid rgba(30, 41, 59, 0.1)', // 改为与其他组件一致的边框色
        borderRadius: '20px',
        boxShadow: '0 10px 40px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.06)',
        display: 'flex',
        flexDirection: 'column',
        ...style,
      }}
    >
      <CardContent style={{ padding: 0, display: 'flex', flexDirection: 'column', height: '100%' }}>
        {/* 消息列表 */}
        <ScrollArea
          className="scrollable-area" // 添加滚动条样式类
          style={{
            flex: 1,
            height: '100%',
            maxHeight: '400px', // 限制最大高度，防止破坏页面布局
            minHeight: '200px', // 设置最小高度
          }}
        >
          <div style={{ padding: '20px' }}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              {messages.map(message => (
                <div
                  key={message.id}
                  style={{
                    display: 'flex',
                    justifyContent: message.role === 'user' ? 'flex-end' : 'flex-start',
                    alignItems: 'flex-end',
                    gap: '12px',
                  }}
                >
                  {/* 助手头像 - 左侧 */}
                  {message.role === 'assistant' && (
                    <div
                      style={{
                        width: layoutMode === 'care' ? '44px' : '36px', // 关爱模式增大头像
                        height: layoutMode === 'care' ? '44px' : '36px',
                        borderRadius: '50%',
                        background: 'linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexShrink: 0,
                        boxShadow: '0 2px 8px rgba(0, 122, 255, 0.3)',
                        marginBottom: '4px',
                      }}
                    >
                      <MessageCircle
                        size={layoutMode === 'care' ? 24 : 20}
                        style={{ color: 'white' }}
                      />
                    </div>
                  )}

                  {/* 消息气泡 */}
                  <div
                    className={message.status === 'streaming' ? 'streaming-message' : ''}
                    style={{
                      maxWidth: '70%',
                      padding: layoutMode === 'care' ? '16px 20px' : '12px 16px', // 关爱模式增加内边距
                      borderRadius:
                        message.role === 'user' ? '18px 18px 4px 18px' : '18px 18px 18px 4px',
                      backgroundColor:
                        message.role === 'user'
                          ? 'rgba(0, 122, 255, 0.9)'
                          : message.role === 'system'
                            ? 'rgba(52, 199, 89, 0.2)'
                            : 'rgba(255, 255, 255, 0.9)',
                      color:
                        message.role === 'user'
                          ? 'white'
                          : message.role === 'system'
                            ? '#34C759'
                            : '#1e293b',
                      backdropFilter: 'blur(8px)',
                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                      position: 'relative',
                    }}
                  >
                    <p
                      style={{
                        fontSize: layoutMode === 'care' ? '24px' : '18px', // 关爱模式24px，普通模式18px
                        lineHeight: '1.5',
                        margin: 0,
                      }}
                    >
                      {message.content}
                      {message.status === 'streaming' && (
                        <span
                          style={{
                            color:
                              message.role === 'user'
                                ? 'rgba(255, 255, 255, 0.7)'
                                : message.role === 'system'
                                  ? 'rgba(52, 199, 89, 0.7)'
                                  : 'rgba(30, 41, 59, 0.7)',
                            marginLeft: '4px',
                          }}
                        >
                          ▋
                        </span>
                      )}
                    </p>
                  </div>

                  {/* 用户头像 - 右侧 */}
                  {message.role === 'user' && (
                    <div
                      style={{
                        width: layoutMode === 'care' ? '44px' : '36px', // 关爱模式增大头像
                        height: layoutMode === 'care' ? '44px' : '36px',
                        borderRadius: '50%',
                        background: 'linear-gradient(135deg, #34C759 0%, #30D158 100%)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexShrink: 0,
                        boxShadow: '0 2px 8px rgba(52, 199, 89, 0.3)',
                        marginBottom: '4px',
                      }}
                    >
                      <User size={layoutMode === 'care' ? 24 : 20} style={{ color: 'white' }} />
                    </div>
                  )}
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          </div>
        </ScrollArea>

        {/* 输入区域 */}
        <div
          style={{
            padding: '16px 20px 20px 20px', // 增加底部内边距，减少空余
            borderTop: '1px solid rgba(30, 41, 59, 0.1)', // 改为与其他组件一致的分割线色
            background: 'rgba(255, 255, 255, 0.5)',
            backdropFilter: 'blur(10px)',
            borderBottomLeftRadius: '20px', // 添加底部左圆角
            borderBottomRightRadius: '20px', // 添加底部右圆角
            marginTop: 'auto', // 确保输入框紧贴底部
          }}
        >
          <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
            <Input
              value={inputValue}
              onChange={e => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="输入您的问题..."
              disabled={isLoading}
              style={{
                flex: 1,
                backgroundColor: 'rgba(255, 255, 255, 0.9)', // 增加背景不透明度，使输入框更明显
                border: '2px solid rgba(0, 122, 255, 0.3)', // 添加蓝色边框，使输入框更明显
                color: '#1e293b',
                fontSize: layoutMode === 'care' ? '24px' : '18px', // 关爱模式24px，普通模式18px
                padding: layoutMode === 'care' ? '16px 20px' : '12px 16px', // 关爱模式增加内边距
                borderRadius: '12px',
                boxShadow: '0 2px 8px rgba(0, 122, 255, 0.1)', // 添加阴影
                transition: 'all 0.3s ease',
              }}
              onFocus={e => {
                e.target.style.border = '2px solid rgba(0, 122, 255, 0.6)';
                e.target.style.boxShadow = '0 4px 12px rgba(0, 122, 255, 0.2)';
              }}
              onBlur={e => {
                e.target.style.border = '2px solid rgba(0, 122, 255, 0.3)';
                e.target.style.boxShadow = '0 2px 8px rgba(0, 122, 255, 0.1)';
              }}
            />
            <button
              onClick={handleSend}
              disabled={!inputValue.trim() || isLoading}
              className="btn-base btn-primary btn-small"
              style={{
                padding: layoutMode === 'care' ? '16px' : '12px', // 关爱模式增加内边距
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                minWidth: layoutMode === 'care' ? '52px' : '44px', // 关爱模式增大按钮
                height: layoutMode === 'care' ? '52px' : '44px',
              }}
            >
              <Send size={layoutMode === 'care' ? 20 : 16} /> {/* 关爱模式增大图标 */}
            </button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
