/**
 * AI服务客户端
 * 连接到真实的AI服务器，支持HTTP请求和SSE流式响应
 */

import { EventBus } from '../core/EventBus';
import { JsonRpcMessageHandler } from '../jsonrpc/JsonRpcMessageHandler';
import { HttpTransport } from '../transport/HttpTransport';
import { SSETransport } from '../transport/SSETransport';
import { generateUUID } from '../utils/helpers';
import { Logger, LogLevel } from '../utils/Logger';

/**
 * AI客户端配置
 */
export interface AIClientConfig {
  /** AI服务器URL */
  url: string;
  /** 请求超时时间（毫秒） */
  timeout?: number;
  /** 重试配置 */
  retry?: {
    maxAttempts: number;
    delay: number;
  };
  /** 调试模式 */
  debug?: boolean;
}

/**
 * 聊天请求参数
 */
export interface ChatRequest {
  /** 用户输入 */
  userInput: string;
  /** 会话ID */
  sessionId?: string;
  /** 请求ID */
  requestId?: string;
}

/**
 * 聊天响应
 */
export interface ChatResponse {
  /** 响应消息 */
  message: string;
  /** 会话ID */
  sessionId: string;
  /** 请求ID */
  requestId?: string;
  /** 状态 */
  status?: string;
}

/**
 * AI服务事件
 */
export interface AIServiceEvents {
  'chat-response': ChatResponse;
  'chat-stream-chunk': { message: string; requestId: string; sessionId?: string };
  'chat-stream-complete': { message: string; requestId: string; sessionId: string };
  'chat-stream-error': { error: any; requestId: string };
  'chat-stream-status': { message: string; requestId: string };
  'chat-error': { error: Error; requestId: string };
}

/**
 * AI服务客户端
 */
export class AIClient {
  private config: Required<AIClientConfig>;
  private eventBus: EventBus;
  private logger: Logger;
  private httpTransport: HttpTransport;
  private sseTransport: SSETransport;
  private jsonRpcHandler: JsonRpcMessageHandler;
  private activeStreams = new Map<string, boolean>();

  // 请求ID到会话ID的映射（用于验证响应匹配）
  private requestSessionMap = new Map<string, string>();

  constructor(config: AIClientConfig, eventBus: EventBus) {
    this.config = {
      timeout: 30000,
      retry: {
        maxAttempts: 3,
        delay: 1000,
      },
      debug: false,
      ...config,
    };

    this.eventBus = eventBus;
    this.logger = Logger.getInstance({
      level: this.config.debug ? LogLevel.DEBUG : LogLevel.INFO,
      prefix: 'AIClient',
    });

    // 创建JSON-RPC消息处理器
    this.jsonRpcHandler = new JsonRpcMessageHandler();

    // 创建HTTP传输层
    this.httpTransport = new HttpTransport(
      {
        baseURL: this.config.url,
        timeout: this.config.timeout,
        retry: this.config.retry,
        debug: this.config.debug,
      },
      this.eventBus
    );

    // 创建SSE传输层
    this.sseTransport = new SSETransport(
      {
        baseURL: this.config.url,
        timeout: this.config.timeout,
        debug: this.config.debug,
      },
      this.eventBus
    );

    this.bindEvents();
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    // 监听SSE消息
    this.eventBus.on('sse:message', (data: any) => {
      this.handleSSEMessage(data.data);
    });

    // 监听SSE错误
    this.eventBus.on('sse:error', (data: any) => {
      this.handleSSEError(data.error);
    });
  }

  /**
   * 发送聊天请求（标准SSE模式）
   */
  public async sendChatRequest(request: ChatRequest): Promise<void> {
    try {
      const requestId = request.requestId || generateUUID();

      this.logger.info('发送聊天请求 (标准SSE)', {
        userInput: request.userInput.substring(0, 50) + '...',
        sessionId: request.sessionId,
        requestId,
      });

      // 检查是否已有活跃流
      if (this.activeStreams.has(requestId)) {
        this.logger.warn('请求已在处理中', { requestId });
        return;
      }

      // 记录请求和会话ID的映射
      this.requestSessionMap.set(requestId, request.sessionId || '');
      this.activeStreams.set(requestId, true);

      // 使用JsonRpcMessageHandler构建请求
      const jsonRpcRequest = this.jsonRpcHandler.createRequest(
        'chat',
        {
          userInput: request.userInput,
          sessionId: request.sessionId || '',
        },
        requestId,
        {
          deviceId: '1234567890',
          organizationId: 1,
          locale: 'zh-CN',
        }
      );

      // 直接通过SSE发送POST请求，获取流式响应
      await this.sseTransport.connect('/rpc', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(jsonRpcRequest),
      });

      this.logger.info('聊天请求已发送，等待流式响应', { requestId });
    } catch (error) {
      const err = error as Error;
      const requestId = request.requestId || 'unknown';

      this.logger.error('聊天请求失败', { error: err.message, request });

      // 清理状态
      this.activeStreams.delete(requestId);
      this.requestSessionMap.delete(requestId);

      this.emitServiceEvent('chat-error', {
        error: err,
        requestId,
      });

      throw err;
    }
  }

  /**
   * 发送通用JSON-RPC请求
   */
  public async sendJsonRpcRequest<T = any>(method: string, params: unknown): Promise<T> {
    try {
      const requestId = generateUUID();

      this.logger.info(`发送${method}请求`, {
        params,
        requestId,
      });

      // 使用JsonRpcMessageHandler构建请求
      const jsonRpcRequest = this.jsonRpcHandler.createRequest(method, params, requestId, {
        deviceId: '1234567890',
        organizationId: 1,
        locale: 'zh-CN',
      });

      // 发送HTTP请求
      const response = await this.httpTransport.post<any>('/rpc', jsonRpcRequest);

      if (response.data.error) {
        throw new Error(`AI服务错误: ${response.data.error.message}`);
      }

      const result = response.data.result;

      this.logger.info(`${method}请求成功`, {
        requestId,
        result,
      });

      return result;
    } catch (error) {
      const err = error as Error;
      this.logger.error(`${method}请求失败`, { error: err.message, params });
      throw err;
    }
  }

  /**
   * 发送背景信息更新请求
   */
  public async sendUpdateBackgroundInfo(params: {
    sessionId?: string;
    page?: string;
    status?: string;
  }): Promise<{ sessionId: string }> {
    return this.sendJsonRpcRequest('updateBackgroundInfo', params);
  }

  /**
   * 发送添加消息请求
   */
  public async sendAddMessages(params: {
    sessionId?: string;
    messages: Array<{
      role: 'user' | 'assistant';
      content: string;
    }>;
  }): Promise<{ sessionId: string }> {
    return this.sendJsonRpcRequest('addMessages', params);
  }

  /**
   * 发送推送业务数据请求
   */
  public async sendPushBizData(params: {
    key: string;
    data: unknown;
  }): Promise<{ success: boolean }> {
    return this.sendJsonRpcRequest('pushBizData', params);
  }

  /**
   * 停止流式响应监听
   */
  public stopStreamListening(requestId: string): void {
    if (this.activeStreams.has(requestId)) {
      this.logger.info('停止流式响应监听', { requestId });
      this.activeStreams.delete(requestId);
      this.sseTransport.disconnect();
    }
  }

  /**
   * 处理SSE消息
   */
  private handleSSEMessage(data: string): void {
    try {
      // SSE原始数据日志已移至传输层

      // 解析SSE数据
      const message = JSON.parse(data);

      // JSON-RPC消息解析日志已移至传输层

      if (message.jsonrpc === '2.0') {
        if (message.method === 'notifications/chatStreamResponse') {
          // 聊天流式响应通知 - 根据json-rpc.md文档
          this.logger.debug('📤 处理聊天流式响应', {
            requestId: message.params?.requestId,
            messageLength: message.params?.message?.length,
          });
          this.handleStreamProgress(message.params);
        } else if (message.method === 'notifications/status') {
          // 状态通知 - 根据json-rpc.md文档
          this.logger.debug('📋 处理状态通知', {
            requestId: message.params?.requestId,
            message: message.params?.message,
          });
          this.handleStatusNotification(message.params);
        } else if (message.id && message.result) {
          // 最终响应
          this.logger.info('✅ 处理JSON-RPC最终响应', {
            id: message.id,
            resultMessage: message.result?.message,
            sessionId: message.result?.sessionId,
          });
          this.handleStreamComplete(message);
        } else if (message.id && message.error) {
          // 错误响应
          this.logger.error('❌ 处理JSON-RPC错误响应', {
            id: message.id,
            errorCode: message.error?.code,
            errorMessage: message.error?.message,
          });
          this.handleStreamErrorResponse(message);
        } else {
          this.logger.warn('⚠️ 未知的JSON-RPC消息格式', { message });
        }
      } else {
        this.logger.warn('⚠️ 非JSON-RPC 2.0消息', { message });
      }
    } catch (error) {
      this.logger.error('❌ 处理SSE消息失败', { error, data });
    }
  }

  /**
   * 处理流式进度
   */
  private handleStreamProgress(params: any): void {
    const { message, requestId, sessionId } = params;

    if (!this.activeStreams.has(requestId)) {
      this.logger.debug('忽略非活跃流的进度消息', { requestId });
      return;
    }

    // 验证会话ID匹配（防止AI服务器返回错误会话的响应）
    const expectedSessionId = this.getExpectedSessionId(requestId);
    if (expectedSessionId && sessionId && sessionId !== expectedSessionId) {
      this.logger.warn('🚫 会话ID不匹配，忽略响应', {
        requestId,
        expectedSessionId,
        receivedSessionId: sessionId,
        message: message?.substring(0, 50) + '...',
      });
      return;
    }

    this.logger.debug('📈 处理流式进度消息', {
      requestId,
      sessionId,
      message: message || '(空消息)',
      messageLength: message?.length || 0,
    });

    // 发送流式块事件
    this.emitServiceEvent('chat-stream-chunk', { message, requestId, sessionId });
  }

  /**
   * 处理状态通知
   */
  private handleStatusNotification(params: any): void {
    const { requestId, message } = params;

    this.logger.debug('📋 处理状态通知', {
      requestId,
      message: message || '(空消息)',
    });

    // 状态通知不需要检查活跃流，只是状态信息
    // 发送状态通知事件，但不作为流式消息处理
    this.emitServiceEvent('chat-stream-status', { message, requestId });

    // 同时发送通知给SDK内部监听器
    this.eventBus.emit('jsonrpc:notification', {
      jsonrpc: '2.0',
      method: 'notifications/status',
      params: {
        requestId,
        message,
      },
    });
  }

  /**
   * 处理流式错误响应
   */
  private handleStreamErrorResponse(response: any): void {
    const { id: requestId, error } = response;

    this.logger.error('❌ 流式响应错误', { requestId, error });

    // 清理活跃流
    if (requestId) {
      this.activeStreams.delete(requestId);
    }

    // 检查是否是编码错误，尝试恢复
    const errorMessage = error?.message || '未知错误';
    const isEncodingError = errorMessage.includes('utf-8') || errorMessage.includes('decode');

    if (isEncodingError) {
      this.logger.warn('检测到编码错误，尝试恢复流式响应', { requestId });

      // 发送恢复消息，让UI能够正常结束
      this.eventBus.emit('ai:chat-final-response', {
        requestId,
        message: '抱歉，响应过程中出现编码问题，请重新提问。',
        sessionId: response.result?.sessionId,
        isError: true,
      });
    } else {
      this.emitServiceEvent('chat-stream-error', {
        error,
        requestId,
      });
    }

    // 断开SSE连接
    this.sseTransport.disconnect();
  }

  /**
   * 处理流式完成
   */
  private handleStreamComplete(response: any): void {
    const { id: requestId, result } = response;
    const { message, sessionId } = result;

    // 验证会话ID匹配（防止AI服务器返回错误会话的响应）
    const expectedSessionId = this.getExpectedSessionId(requestId);
    if (expectedSessionId && sessionId && sessionId !== expectedSessionId) {
      this.logger.warn('🚫 最终响应会话ID不匹配，忽略响应', {
        requestId,
        expectedSessionId,
        receivedSessionId: sessionId,
        message: message?.substring(0, 50) + '...',
      });
      return;
    }

    this.logger.info('流式响应完成', { requestId, sessionId });

    // 发送AI响应通知给SDK内部监听器
    this.eventBus.emit('jsonrpc:notification', {
      jsonrpc: '2.0',
      method: 'notifications/aiResponse',
      params: {
        requestId,
        message,
        sessionId,
        // 根据文档，可能包含action和data字段
        ...(result.action && { action: result.action }),
        ...(result.data && { data: result.data }),
      },
    });

    // 清理活跃流和会话映射
    this.activeStreams.delete(requestId);
    this.requestSessionMap.delete(requestId);

    this.emitServiceEvent('chat-stream-complete', {
      message,
      requestId,
      sessionId,
    });

    // 断开SSE连接
    this.sseTransport.disconnect();
  }

  /**
   * 处理SSE错误
   */
  private handleSSEError(error: Error): void {
    this.logger.error('SSE连接错误', error);

    // 清理所有活跃流
    for (const requestId of this.activeStreams.keys()) {
      this.emitServiceEvent('chat-error', { error, requestId });
    }

    this.activeStreams.clear();
  }

  /**
   * 发送服务事件
   */
  private emitServiceEvent<K extends keyof AIServiceEvents>(
    event: K,
    data: AIServiceEvents[K]
  ): void {
    this.eventBus.emit(`ai:${event}`, data);
  }

  /**
   * 获取活跃流数量
   */
  public getActiveStreamCount(): number {
    return this.activeStreams.size;
  }

  /**
   * 获取请求对应的期望会话ID
   */
  private getExpectedSessionId(requestId: string): string | undefined {
    return this.requestSessionMap.get(requestId);
  }

  /**
   * 检查是否有活跃流
   */
  public hasActiveStreams(): boolean {
    return this.activeStreams.size > 0;
  }

  /**
   * 停止所有活跃流
   */
  public stopAllStreams(): void {
    this.logger.info('停止所有活跃流', { count: this.activeStreams.size });
    this.activeStreams.clear();
    this.sseTransport.disconnect();
  }

  /**
   * 销毁客户端
   */
  public destroy(): void {
    this.stopAllStreams();
    this.sseTransport.destroy();
    this.logger.info('AI客户端已销毁');
  }
}
