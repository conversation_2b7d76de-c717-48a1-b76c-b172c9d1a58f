/**
 * JSON-RPC 2.0 验证模块
 * 负责验证请求和响应格式是否符合规范
 */

import { Logger } from '../utils/Logger';

/**
 * JSON-RPC 2.0 验证器
 */
export class JsonRpcValidator {
  private logger: Logger;

  constructor() {
    this.logger = Logger.getInstance({ prefix: 'JsonRpcValidator' });
  }

  /**
   * 验证JSON-RPC 2.0请求格式（增强版本）
   */
  public validateRequest(request: unknown): boolean {
    if (!request || typeof request !== 'object') {
      this.logger.warn('请求不是有效的对象');
      return false;
    }

    const req = request as any;

    // 检查必需字段
    if (req.jsonrpc !== '2.0') {
      this.logger.warn('缺少或无效的jsonrpc字段，必须为"2.0"');
      return false;
    }

    if (!req.method || typeof req.method !== 'string') {
      this.logger.warn('缺少或无效的method字段，必须为非空字符串');
      return false;
    }

    // 验证method字段格式（不能包含特殊字符）
    if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(req.method)) {
      this.logger.warn('method字段格式无效，只能包含字母、数字和下划线，且必须以字母开头');
      return false;
    }

    // id字段验证（必须存在且不能为null/undefined）
    if (!('id' in req) || req.id === null || req.id === undefined) {
      this.logger.warn('id字段是必需的，不能为null或undefined');
      return false;
    }

    // id字段类型验证
    if (typeof req.id !== 'string' && typeof req.id !== 'number') {
      this.logger.warn('id字段必须为字符串或数字类型');
      return false;
    }

    // params字段验证（可选，但如果存在必须是有效的JSON值）
    if ('params' in req && req.params !== undefined) {
      try {
        JSON.stringify(req.params);
      } catch {
        this.logger.warn('params字段必须是可序列化的JSON值');
        return false;
      }
    }

    return true;
  }

  /**
   * 验证JSON-RPC 2.0响应格式
   */
  public validateResponse(response: unknown): boolean {
    if (!response || typeof response !== 'object') {
      this.logger.warn('响应不是有效的对象');
      return false;
    }

    const resp = response as any;

    // 检查必需字段
    if (resp.jsonrpc !== '2.0') {
      this.logger.warn('响应缺少或无效的jsonrpc字段');
      return false;
    }

    if (!resp.id) {
      this.logger.warn('响应缺少id字段');
      return false;
    }

    // 检查响应类型：必须有result或error，但不能同时有
    const hasResult = 'result' in resp;
    const hasError = 'error' in resp;

    if (hasResult && hasError) {
      this.logger.warn('响应不能同时包含result和error字段');
      return false;
    }

    if (!hasResult && !hasError) {
      this.logger.warn('响应必须包含result或error字段之一');
      return false;
    }

    // 如果是错误响应，验证error格式
    if (hasError) {
      return this.validateError(resp.error);
    }

    return true;
  }

  /**
   * 验证JSON-RPC 2.0通知格式
   */
  public validateNotification(notification: unknown): boolean {
    if (!notification || typeof notification !== 'object') {
      this.logger.warn('通知不是有效的对象');
      return false;
    }

    const notif = notification as any;

    // 检查必需字段
    if (notif.jsonrpc !== '2.0') {
      this.logger.warn('通知缺少或无效的jsonrpc字段');
      return false;
    }

    if (!notif.method || typeof notif.method !== 'string') {
      this.logger.warn('通知缺少或无效的method字段');
      return false;
    }

    // 通知不应该有id字段
    if ('id' in notif) {
      this.logger.warn('通知不应该包含id字段');
      return false;
    }

    return true;
  }

  /**
   * 验证错误对象格式
   */
  private validateError(error: unknown): boolean {
    if (!error || typeof error !== 'object') {
      this.logger.warn('error字段不是有效的对象');
      return false;
    }

    const err = error as any;

    if (typeof err.code !== 'number') {
      this.logger.warn('error.code必须是数字');
      return false;
    }

    if (typeof err.message !== 'string') {
      this.logger.warn('error.message必须是字符串');
      return false;
    }

    return true;
  }

  /**
   * 验证方法名是否符合文档规范
   */
  public validateMethodName(method: string): boolean {
    const validMethods = [
      // 客户端发送的方法（根据json-rpc.md文档）
      'speak',
      'updateBackgroundInfo',
      'addMessages',
      'pushBizData',
      'chat', // AI服务器的chat方法
      'speakMode', // HKSTT收音模式设置方法

      // 客户端UI方法（服务端反向请求）
      'client/ui/getUserInput',
    ];

    // 允许以notifications/开头的通知方法
    if (method.startsWith('notifications/')) {
      return true;
    }

    // 检查是否是文档中定义的方法
    if (validMethods.includes(method)) {
      return true;
    }

    // 允许测试方法
    if (method.startsWith('test.')) {
      return true;
    }

    this.logger.warn(`未知的方法名: ${method}`);
    return false;
  }

  /**
   * 获取所有支持的方法列表
   */
  public getSupportedMethods(): string[] {
    return ['speak', 'updateBackgroundInfo', 'addMessages', 'pushBizData', 'speakMode'];
  }

  /**
   * 验证完整的JSON-RPC消息（包含格式和业务逻辑验证）
   */
  public validateJsonRpcMessage(
    message: unknown,
    options: {
      validateMethod?: boolean;
      validateParams?: boolean;
    } = {}
  ): { valid: boolean; error?: string } {
    const { validateMethod = true, validateParams = true } = options;

    // 基础格式验证
    if (!this.validateRequest(message)) {
      return { valid: false, error: '消息格式不符合JSON-RPC 2.0规范' };
    }

    const msg = message as any;

    // 方法名验证
    if (validateMethod && !this.validateMethodName(msg.method)) {
      return {
        valid: false,
        error: `不支持的方法: ${msg.method}，支持的方法: ${this.getSupportedMethods().join(', ')}`,
      };
    }

    // 参数验证
    if (validateParams && !this.validateMethodParams(msg.method, msg.params)) {
      return {
        valid: false,
        error: `方法 ${msg.method} 的参数格式不正确`,
      };
    }

    return { valid: true };
  }

  /**
   * 验证参数结构是否符合特定方法的要求
   */
  public validateMethodParams(method: string, params: unknown): boolean {
    switch (method) {
      case 'speak':
        return this.validateSpeakParams(params);
      case 'updateBackgroundInfo':
        return this.validateUpdateBackgroundInfoParams(params);
      case 'addMessages':
        return this.validateAddMessagesParams(params);
      case 'pushBizData':
        return this.validatePushBizDataParams(params);
      case 'speakMode':
        return this.validateSpeakModeParams(params);
      default:
        // 对于未知方法，不进行严格验证
        return true;
    }
  }

  private validateSpeakParams(params: unknown): boolean {
    if (!params || typeof params !== 'object') {
      return false;
    }

    const p = params as any;
    return typeof p.text === 'string' && p.text.length > 0;
  }

  private validateUpdateBackgroundInfoParams(params: unknown): boolean {
    if (!params || typeof params !== 'object') {
      return false;
    }

    // 根据文档，sessionId是可选的，page和status也是可选的
    // 只要是对象就认为有效，具体字段由服务端验证
    return true;
  }

  private validateAddMessagesParams(params: unknown): boolean {
    if (!params || typeof params !== 'object') {
      return false;
    }

    const p = params as any;
    return Array.isArray(p.messages) && p.messages.length > 0;
  }

  private validatePushBizDataParams(params: unknown): boolean {
    if (!params || typeof params !== 'object') {
      return false;
    }

    const p = params as any;
    // 根据文档，应该是key和data字段，不是type和data
    return typeof p.key === 'string' && p.data !== undefined;
  }

  private validateSpeakModeParams(params: unknown): boolean {
    if (!params || typeof params !== 'object') {
      return false;
    }

    const p = params as any;
    // speakMode必须是0（唇动收音）或1（按键收音）
    return typeof p.speakMode === 'number' && (p.speakMode === 0 || p.speakMode === 1);
  }
}
