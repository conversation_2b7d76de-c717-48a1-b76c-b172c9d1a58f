/**
 * JSON-RPC 通知管理器
 * 负责管理通知监听器和处理特殊通知
 */

import { EventBus } from '../core/EventBus';
import { Logger } from '../utils/Logger';

import { JsonRpcValidator } from './JsonRpcValidator';

/**
 * 通知回调函数类型
 */
export type NotificationCallback = (params: unknown) => void;

/**
 * JSON-RPC 通知管理器
 */
export class JsonRpcNotificationManager {
  private eventBus: EventBus;
  private logger: Logger;
  private validator: JsonRpcValidator;
  private notificationListeners = new Map<string, NotificationCallback[]>();

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
    this.logger = Logger.getInstance({ prefix: 'JsonRpcNotificationManager' });
    this.validator = new JsonRpcValidator();

    this.setupEventListeners();
  }

  /**
   * 监听JSON-RPC通知
   * @param method 通知方法名
   * @param callback 回调函数
   * @returns 取消监听的函数
   */
  public onNotification(method: string, callback: NotificationCallback): () => void {
    if (!this.notificationListeners.has(method)) {
      this.notificationListeners.set(method, []);
    }

    const listeners = this.notificationListeners.get(method);
    if (listeners) {
      listeners.push(callback);

      this.logger.info('📝 注册通知监听器', {
        method,
        listenerCount: listeners.length,
      });
    }

    // 返回取消监听的函数
    return () => {
      const currentListeners = this.notificationListeners.get(method);
      if (currentListeners) {
        const index = currentListeners.indexOf(callback);
        if (index > -1) {
          currentListeners.splice(index, 1);
          this.logger.info('🗑️ 移除通知监听器', {
            method,
            remainingCount: currentListeners.length,
          });

          // 如果没有监听器了，删除整个条目
          if (currentListeners.length === 0) {
            this.notificationListeners.delete(method);
          }
        }
      }
    };
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    this.logger.info('🗑️ 销毁通知管理器');

    // 清空所有监听器
    this.notificationListeners.clear();

    // 清理事件监听器
    this.eventBus.off('jsonrpc:notification', this.handleNotification);
    this.eventBus.off('client:send-notification', this.handleNotification);

    this.logger.info('✅ 通知管理器已销毁');
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听JSON-RPC通知
    this.eventBus.on('jsonrpc:notification', this.handleNotification);
    this.eventBus.on('client:send-notification', this.handleNotification);
  }

  /**
   * 处理收到的JSON-RPC通知
   */
  private handleNotification = (notification: unknown): void => {
    // 验证通知格式
    if (!this.validator.validateNotification(notification)) {
      this.logger.warn('收到无效的JSON-RPC通知', { notification });
      return;
    }

    const notif = notification as any;
    const method = notif.method;
    const params = notif.params;

    this.logger.info('📥 收到JSON-RPC通知', {
      method,
      hasParams: params !== undefined,
    });

    // 处理特殊的通知类型
    this.handleSpecialNotifications(method, params);

    // 查找并调用监听器
    const listeners = this.notificationListeners.get(method);
    if (listeners && listeners.length > 0) {
      listeners.forEach(callback => {
        try {
          callback(params);
        } catch (error) {
          this.logger.error('通知监听器执行失败', { method, error });
        }
      });
    } else {
      this.logger.debug('没有找到通知监听器', { method });
    }
  };

  /**
   * 处理特殊的通知类型（根据json-rpc.md文档）
   */
  private handleSpecialNotifications(method: string, params: any): void {
    switch (method) {
      case 'notifications/status':
        this.handleStatusNotification(params);
        break;

      case 'notifications/chatStreamResponse':
        this.handleChatStreamResponse(params);
        break;

      case 'notifications/userInput':
        this.handleUserInputNotification(params);
        break;

      case 'notifications/aiResponse':
        this.handleAIResponseNotification(params);
        break;

      case 'notifications/newUser':
        this.handleNewUserNotification(params);
        break;

      case 'notifications/faceStatus':
        this.handleFaceStatusNotification(params);
        break;

      case 'notifications/modelStatus':
        this.handleModelStatusNotification(params);
        break;

      case 'notifications/asrOfflineResult':
        this.handleASROfflineResultNotification(params);
        break;

      case 'notifications/asrSessionComplete':
        this.handleASRSessionCompleteNotification(params);
        break;
    }
  }

  private handleStatusNotification(params: any): void {
    const { requestId, message } = params;

    this.logger.debug('� 状态通知', { requestId, message });

    // 状态通知不应该输入到对话框中，只是记录状态
    this.eventBus.emit('ai:status', {
      requestId,
      message,
      timestamp: Date.now(),
    });
  }

  private handleAIResponseNotification(params: any): void {
    const { requestId, message, action, data } = params;

    this.logger.info('🤖 AI响应通知', {
      requestId,
      message: message?.substring(0, 50) + '...',
      action,
      hasData: !!data,
    });

    // 转发AI响应通知给客户端
    this.eventBus.emit('client:send-notification', {
      jsonrpc: '2.0',
      method: 'notifications/aiResponse',
      params: {
        requestId,
        message,
        action,
        data,
      },
    });
  }

  private handleChatStreamResponse(params: any): void {
    const { message, requestId, sessionId } = params;

    this.logger.debug('💬 聊天流式响应', {
      requestId,
      sessionId,
      messageLength: message?.length,
    });

    this.eventBus.emit('tts:stream-text', {
      text: message,
      requestId,
      sessionId,
    });
  }

  private handleUserInputNotification(params: any): void {
    const { userInput, requestId, sessionId } = params;

    this.logger.info('🎤 用户语音输入', {
      requestId,
      sessionId,
      inputLength: userInput?.length,
    });

    this.eventBus.emit('user:input', {
      userInput,
      requestId,
      sessionId,
      timestamp: Date.now(),
    });
  }

  private handleNewUserNotification(params: any): void {
    this.logger.info('👤 新用户检测', params);

    this.eventBus.emit('user:new', {
      ...params,
      timestamp: Date.now(),
    });
  }

  private handleFaceStatusNotification(params: any): void {
    const { hasFace } = params;

    this.logger.info('👁️ 人脸状态变化', { hasFace });

    this.eventBus.emit('face:status', {
      hasFace,
      timestamp: Date.now(),
    });
  }

  private handleModelStatusNotification(params: any): void {
    const { loaded } = params;

    this.logger.info('🤖 模型状态', { loaded });

    this.eventBus.emit('model:status', {
      loaded,
      timestamp: Date.now(),
    });
  }

  private handleASROfflineResultNotification(params: any): void {
    const { sid, text } = params;

    this.logger.info('🎯 ASR识别结果', {
      sid,
      textLength: text?.length,
    });

    this.eventBus.emit('asr:result', {
      sid,
      text,
      timestamp: Date.now(),
    });
  }

  private handleASRSessionCompleteNotification(params: any): void {
    this.logger.info('✅ ASR会话完成', params);

    this.eventBus.emit('asr:session:complete', {
      ...params,
      timestamp: Date.now(),
    });
  }
}
