/**
 * 增强型流式音频播放器
 * 支持无缝播放任意数量的音频块
 * 使用动态播放器池和智能缓冲管理
 */

import { EventBus } from '../core/EventBus';
import { Logger } from '../utils/Logger';

import type { AudioChunk } from './types';

/**
 * 播放器状态
 */
export enum PlayerState {
  IDLE = 'idle',
  LOADING = 'loading',
  PLAYING = 'playing',
  PAUSED = 'paused',
  ENDED = 'ended',
  ERROR = 'error',
}

/**
 * 播放器配置
 */
export interface StreamingAudioPlayerConfig {
  /** 事件总线 */
  eventBus: EventBus;
  /** 音频缓冲大小 */
  bufferSize?: number;
  /** 播放器池大小 */
  playerPoolSize?: number;
  /** 预加载音频块数量 */
  preloadChunks?: number;
  /** 是否启用调试日志 */
  debug?: boolean;
}

/**
 * 音频播放器实例
 */
interface AudioPlayerInstance {
  id: string;
  player: HTMLAudioElement;
  url: string | null;
  isActive: boolean;
  chunkId: number | null;
}

/**
 * 增强型流式音频播放器
 * 核心改进：
 * 1. 动态播放器池管理，支持任意数量音频块
 * 2. 智能预加载和缓冲策略
 * 3. 无缝音频切换和播放
 * 4. 内存优化和资源管理
 */
export class StreamingAudioPlayer {
  private eventBus: EventBus;
  private logger: Logger;
  private currentState: PlayerState = PlayerState.IDLE;

  // 音频队列和播放管理
  private audioQueue: { data: Uint8Array; info: AudioChunk }[] = [];
  private isPlaying: boolean = false;
  private hasStartedPlaying: boolean = false;
  private hasEmittedPlayStart: boolean = false; // 是否已发送play-start事件
  private isReceivingComplete: boolean = false;
  private currentPlayingIndex: number = 0;

  // 轻量级缓冲策略
  private startTime: number = 0;

  // WAV头部缓存（避免重复创建）
  private cachedWavHeader: Uint8Array | null = null;

  // 用户交互状态（解决自动播放策略）
  private userHasInteracted: boolean = false;

  // 动态播放器池
  private playerPool: AudioPlayerInstance[] = [];
  private playerPoolSize: number;

  constructor(config: StreamingAudioPlayerConfig) {
    this.eventBus = config.eventBus;
    this.logger = Logger.getInstance({
      prefix: 'StreamingAudioPlayer',
    });

    // 配置参数
    this.playerPoolSize = config.playerPoolSize || 3; // 默认3个播放器

    // 初始化播放器池
    this.initializePlayerPool();

    // 监听用户交互以激活音频上下文
    this.setupUserInteractionListeners();

    // 监听EventBus的用户交互事件
    this.setupEventBusListeners();
  }

  /**
   * 设置用户交互监听器（解决自动播放策略）
   */
  private setupUserInteractionListeners(): void {
    const activateAudio = () => {
      if (!this.userHasInteracted) {
        this.userHasInteracted = true;
        this.logger.info('用户交互检测到，音频播放已激活');

        // 移除监听器，只需要激活一次
        document.removeEventListener('click', activateAudio);
        document.removeEventListener('touchstart', activateAudio);
        document.removeEventListener('keydown', activateAudio);
      }
    };

    // 监听各种用户交互事件
    document.addEventListener('click', activateAudio, { once: true });
    document.addEventListener('touchstart', activateAudio, { once: true });
    document.addEventListener('keydown', activateAudio, { once: true });
  }

  /**
   * 设置EventBus用户交互监听器
   */
  private setupEventBusListeners(): void {
    // 监听通过EventBus发送的用户交互事件
    this.eventBus.on('user:interaction-detected', (data: any) => {
      if (!this.userHasInteracted) {
        this.userHasInteracted = true;
        this.logger.info('通过EventBus检测到用户交互，音频播放已激活', { data });

        // 立即预激活音频上下文
        this.preActivateAudioContext();
      }
    });
  }

  /**
   * 预激活音频上下文（在用户交互时立即执行）
   */
  private preActivateAudioContext(): void {
    try {
      // 创建一个临时的静音音频并播放，激活音频上下文
      const tempAudio = new Audio();
      tempAudio.volume = 0.01; // 极低音量
      tempAudio.src =
        'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmHgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';

      tempAudio
        .play()
        .then(() => {
          this.logger.info('🎵 音频上下文预激活成功');
          tempAudio.pause();
          tempAudio.currentTime = 0;
        })
        .catch(error => {
          this.logger.warn('音频上下文预激活失败，但用户交互已记录', { error });
        });
    } catch (error) {
      this.logger.warn('预激活音频上下文时出错', { error });
    }
  }

  /**
   * 初始化播放器池
   */
  private initializePlayerPool(): void {
    this.playerPool = [];

    for (let i = 0; i < this.playerPoolSize; i++) {
      const playerId = `player_${i}`;
      const player = new Audio();

      const playerInstance: AudioPlayerInstance = {
        id: playerId,
        player,
        url: null,
        isActive: false,
        chunkId: null,
      };

      this.setupPlayerEvents(playerInstance);
      this.playerPool.push(playerInstance);
    }
  }

  /**
   * 获取可用的播放器实例
   */
  private getAvailablePlayer(): AudioPlayerInstance | null {
    return this.playerPool.find(p => !p.isActive) || null;
  }

  /**
   * 添加音频数据块
   */
  public addAudioChunk(chunk: AudioChunk): void {
    try {
      // 优化的Base64解码
      const bytes = this.fastBase64Decode(chunk.audioData);

      // 记录开始时间
      if (chunk.chunkId === 0) {
        this.startTime = Date.now();
      }

      // 添加到音频队列
      this.audioQueue.push({ data: bytes, info: chunk });

      // 如果还没开始播放，立即开始
      if (!this.isPlaying && this.currentState === PlayerState.LOADING) {
        this.startStreamingPlayback();
      } else if (this.isPlaying && !this.hasStartedPlaying) {
        // 如果正在等待缓冲，检查是否可以开始播放
        this.checkAndStartPlayback();
      }

      // 如果是最后一个音频块，标记完成
      if (chunk.isFinal) {
        this.isReceivingComplete = true;
        this.logger.info(`音频数据接收完成，共 ${this.audioQueue.length} 个音频块`);
      }
    } catch (error) {
      this.logger.error('处理音频块失败', { error, chunkId: chunk.chunkId });
      this.setState(PlayerState.ERROR);
    }
  }

  /**
   * 标记音频接收完成（不添加重复的音频块）
   */
  public markReceivingComplete(): void {
    this.isReceivingComplete = true;
    this.logger.info(`音频数据接收完成，共 ${this.audioQueue.length} 个音频块`);

    // 标记最后一个音频块为final（如果存在）
    if (this.audioQueue.length > 0) {
      const lastAudioItem = this.audioQueue[this.audioQueue.length - 1];
      if (lastAudioItem) {
        lastAudioItem.info.isFinal = true;
        this.logger.debug('已标记最后一个音频块为final', {
          chunkId: lastAudioItem.info.chunkId,
        });
      }
    }

    // 检查是否需要结束播放
    if (this.currentPlayingIndex >= this.audioQueue.length) {
      this.finishPlayback();
    }
  }

  /**
   * 开始流式播放（极简策略：固定1秒延迟）
   */
  private startStreamingPlayback(): void {
    if (this.audioQueue.length === 0 || this.isPlaying) {
      return;
    }

    this.isPlaying = true;
    this.currentPlayingIndex = 0;

    // 极简策略：固定1秒延迟后开始播放
    const FIXED_DELAY = 3000; // 1秒固定延迟

    if (this.isReceivingComplete || Date.now() - this.startTime >= FIXED_DELAY) {
      // 立即开始播放
      this.hasStartedPlaying = true;
      this.logger.info(`开始流式音频播放，当前缓冲: ${this.audioQueue.length} 个音频块`);
      this.playNextChunk();
    } else {
      // 等待固定延迟
      const remainingDelay = FIXED_DELAY - (Date.now() - this.startTime);
      this.logger.info(`等待固定延迟，剩余: ${remainingDelay}ms`);
      setTimeout(() => this.checkAndStartPlayback(), Math.min(remainingDelay, 100));
    }
  }

  /**
   * 检查并开始播放（优化版 - 减少卡顿）
   */
  private checkAndStartPlayback(): void {
    if (this.hasStartedPlaying) {
      return; // 已经开始播放了
    }

    // 优化策略：减少延迟，提高响应性
    const MIN_BUFFER_CHUNKS = 1; // 最少缓冲块数
    const MAX_DELAY = 500; // 最大延迟500ms（减少卡顿）

    const hasMinBuffer = this.audioQueue.length >= MIN_BUFFER_CHUNKS;
    const hasWaitedEnough = Date.now() - this.startTime >= MAX_DELAY;

    if (this.isReceivingComplete || hasMinBuffer || hasWaitedEnough) {
      this.hasStartedPlaying = true;
      this.logger.info(`开始播放，音频块数: ${this.audioQueue.length}`);
      this.playNextChunk();
    } else {
      // 更频繁的检查，减少延迟
      setTimeout(() => this.checkAndStartPlayback(), 50);
    }
  }

  /**
   * 设置播放器事件
   */
  private setupPlayerEvents(playerInstance: AudioPlayerInstance): void {
    const { player, id } = playerInstance;

    player.addEventListener('play', () => {
      if (playerInstance.isActive) {
        this.setState(PlayerState.PLAYING);

        // 只在第一次播放时发送事件
        if (!this.hasEmittedPlayStart) {
          this.hasEmittedPlayStart = true;
          this.eventBus.emit('tts:play-start');
          this.logger.info('🎵 发送TTS播放开始事件');
        }
      }
    });

    player.addEventListener('ended', () => {
      if (playerInstance.isActive) {
        this.logger.debug(`播放器 ${id} 播放结束，音频块 ${playerInstance.chunkId}`);
        this.handlePlayerEnded(playerInstance);
      }
    });

    player.addEventListener('error', event => {
      // 只有在播放器处于活跃状态时才处理错误
      if (playerInstance.isActive && playerInstance.chunkId !== null) {
        this.logger.error(`播放器 ${id} 错误`, { error: event, chunkId: playerInstance.chunkId });
        this.setState(PlayerState.ERROR);
      } else {
        // 忽略初始化时的错误
        this.logger.debug(`播放器 ${id} 初始化时的错误，已忽略`);
      }
    });

    player.addEventListener('loadstart', () => {
      this.logger.debug(`播放器 ${id} 开始加载音频块 ${playerInstance.chunkId}`);
    });

    player.addEventListener('canplay', () => {
      this.logger.debug(`播放器 ${id} 可以播放音频块 ${playerInstance.chunkId}`);
    });
  }

  /**
   * 处理播放器播放结束
   */
  private handlePlayerEnded(playerInstance: AudioPlayerInstance): void {
    this.logger.debug(`播放器 ${playerInstance.id} 播放结束，音频块 ${playerInstance.chunkId}`);

    // 释放当前播放器
    this.releasePlayer(playerInstance);

    // 更新播放索引（只有在音频块真正播放完成后才移动到下一个）
    this.currentPlayingIndex++;

    this.logger.debug(`播放索引已更新: ${this.currentPlayingIndex}/${this.audioQueue.length}`);

    // 立即播放下一个音频块（无延迟）
    this.playNextChunkImmediate();
  }

  /**
   * 立即播放下一个音频块（优化版）
   */
  private playNextChunkImmediate(): void {
    // 直接调用playNextChunk，让它处理所有逻辑
    this.playNextChunk();
  }

  /**
   * 释放播放器资源
   */
  private releasePlayer(playerInstance: AudioPlayerInstance): void {
    // 暂停播放器
    if (!playerInstance.player.paused) {
      playerInstance.player.pause();
    }

    // 清理URL
    if (playerInstance.url) {
      URL.revokeObjectURL(playerInstance.url);
      playerInstance.url = null;
    }

    // 重置播放器状态
    playerInstance.isActive = false;
    playerInstance.chunkId = null;

    // 优化：减少不必要的load()调用，只在必要时重置
    playerInstance.player.removeAttribute('src');
    // 只在播放器有错误状态时才调用load()
    if (playerInstance.player.error) {
      playerInstance.player.load();
    }
  }

  /**
   * 获取可用播放器并准备播放
   */
  private preparePlayerForChunk(chunkIndex: number): AudioPlayerInstance | null {
    if (chunkIndex >= this.audioQueue.length) {
      return null;
    }

    const availablePlayer = this.getAvailablePlayer();
    if (!availablePlayer) {
      this.logger.warn('没有可用的播放器，等待播放器释放');
      return null;
    }

    const queueItem = this.audioQueue[chunkIndex];
    if (!queueItem) {
      this.logger.error('音频队列项不存在', { chunkIndex });
      return null;
    }

    const { data, info } = queueItem;

    try {
      // 创建音频Blob
      const audioBlob = this.createValidWavBlob(data);
      const audioUrl = URL.createObjectURL(audioBlob);

      // 配置播放器
      availablePlayer.url = audioUrl;
      availablePlayer.isActive = true;
      availablePlayer.chunkId = info.chunkId;
      availablePlayer.player.src = audioUrl;

      return availablePlayer;
    } catch (error) {
      this.logger.error('准备播放器失败', { error, chunkId: info.chunkId });
      return null;
    }
  }

  /**
   * 播放下一个音频块
   */
  private playNextChunk(): void {
    this.logger.debug(
      `playNextChunk调用: currentIndex=${this.currentPlayingIndex}, queueLength=${this.audioQueue.length}, isComplete=${this.isReceivingComplete}`
    );

    // 检查是否还有音频块需要播放
    if (this.currentPlayingIndex >= this.audioQueue.length) {
      // 如果队列中没有更多音频块
      if (this.isReceivingComplete) {
        // 如果接收已完成，结束播放
        this.finishPlayback();
      } else {
        // 如果还在接收中，等待更多音频块
        this.logger.debug('等待更多音频块，500ms后重试');
        setTimeout(() => this.playNextChunk(), 500);
      }
      return;
    }

    try {
      // 准备播放器播放当前音频块
      const playerInstance = this.preparePlayerForChunk(this.currentPlayingIndex);

      if (!playerInstance) {
        // 如果没有可用播放器，稍后重试
        this.logger.debug('没有可用播放器，100ms后重试');
        setTimeout(() => this.playNextChunk(), 100);
        return;
      }

      // 开始播放（处理自动播放策略）
      this.playAudioWithFallback(playerInstance);

      // 注意：不在这里更新播放索引，而是在handlePlayerEnded中更新
      // 这样可以确保只有在音频块真正播放完成后才移动到下一个
    } catch (error) {
      this.logger.error('播放音频块失败', { error });
      this.setState(PlayerState.ERROR);
    }
  }

  /**
   * 播放音频（处理自动播放策略）
   */
  private async playAudioWithFallback(playerInstance: AudioPlayerInstance): Promise<void> {
    try {
      await playerInstance.player.play();
    } catch (error: any) {
      if (error.name === 'NotAllowedError') {
        this.logger.warn('自动播放被阻止，等待用户交互', {
          playerId: playerInstance.id,
          chunkId: playerInstance.chunkId,
        });

        // 发送自动播放被阻止事件给UI层
        this.logger.info('🚫 发送自动播放被阻止事件', {
          playerId: playerInstance.id,
          chunkId: playerInstance.chunkId,
        });
        this.eventBus.emit('tts:autoplay-blocked', {
          playerId: playerInstance.id,
          chunkId: playerInstance.chunkId,
        });

        // 如果是自动播放被阻止，等待用户交互后重试
        this.waitForUserInteractionAndPlay(playerInstance);
      } else {
        this.logger.error('音频播放失败', {
          error,
          playerId: playerInstance.id,
          chunkId: playerInstance.chunkId,
        });
        this.releasePlayer(playerInstance);
        this.setState(PlayerState.ERROR);
      }
    }
  }

  /**
   * 等待用户交互后播放
   */
  private waitForUserInteractionAndPlay(playerInstance: AudioPlayerInstance): void {
    let retryCount = 0;
    const maxRetries = 100; // 最多等待10秒（100 * 100ms）

    const tryPlay = async () => {
      if (this.userHasInteracted) {
        try {
          await playerInstance.player.play();
          this.logger.info(`✅ 用户交互后播放成功，播放器: ${playerInstance.id}`);
          // 发送自动播放恢复事件给UI层
          this.eventBus.emit('tts:autoplay-resumed', {
            playerId: playerInstance.id,
            chunkId: playerInstance.chunkId,
          });
        } catch (error: any) {
          if (error.name === 'NotAllowedError') {
            this.logger.warn(`🚫 播放仍被阻止，重试中... (${retryCount}/${maxRetries})`, {
              playerId: playerInstance.id,
              chunkId: playerInstance.chunkId,
            });

            // 如果还是被阻止，继续等待
            if (retryCount < maxRetries) {
              retryCount++;
              setTimeout(tryPlay, 100);
            } else {
              this.logger.error('达到最大重试次数，放弃播放', {
                playerId: playerInstance.id,
                chunkId: playerInstance.chunkId,
              });
              this.releasePlayer(playerInstance);
            }
          } else {
            this.logger.error('用户交互后播放出现其他错误', {
              error,
              playerId: playerInstance.id,
              chunkId: playerInstance.chunkId,
            });
            this.releasePlayer(playerInstance);
          }
        }
      } else {
        // 继续等待用户交互
        if (retryCount < maxRetries) {
          retryCount++;
          setTimeout(tryPlay, 100);
        } else {
          this.logger.error('等待用户交互超时，放弃播放', {
            playerId: playerInstance.id,
            chunkId: playerInstance.chunkId,
          });
          this.releasePlayer(playerInstance);
        }
      }
    };

    tryPlay();
  }

  /**
   * 完成播放
   */
  private finishPlayback(): void {
    this.isPlaying = false;
    this.setState(PlayerState.ENDED);
    this.eventBus.emit('tts:play-end');

    // 检查是否有正在播放的音频块
    const hasActivePlayer = this.playerPool.some(p => p.isActive && !p.player.paused);

    if (hasActivePlayer) {
      this.logger.info(`检测到正在播放的音频块，等待播放完成后清理资源`);
      // 如果有正在播放的音频，等待一段时间后再清理
      setTimeout(() => {
        this.cleanupAudio();
      }, 100);
    } else {
      this.logger.info(`没有正在播放的音频块，立即清理资源`);
      this.cleanupAudio();
    }

    this.logger.info(`所有音频块播放完成，共播放 ${this.currentPlayingIndex} 个音频块`);
  }

  /**
   * 轻量级WAV/PCM混合格式处理
   * 针对TTS返回的"第一块WAV+后续PCM"格式优化
   */
  private createValidWavBlob(audioData: Uint8Array): Blob {
    // 第一个音频块：完整WAV文件，直接使用
    if (this.isCompleteWavFile(audioData)) {
      return new Blob([audioData], { type: 'audio/wav' });
    }

    // 后续音频块：纯PCM数据，添加轻量级WAV头部

    // 使用固定参数创建轻量级WAV头部（基于TTS服务的固定格式）
    const wavHeader = this.createLightweightWavHeader(audioData.length);
    const completeWavData = new Uint8Array(wavHeader.length + audioData.length);

    completeWavData.set(wavHeader, 0);
    completeWavData.set(audioData, wavHeader.length);

    return new Blob([completeWavData], { type: 'audio/wav' });
  }

  /**
   * 创建轻量级WAV头部（使用缓存优化）
   */
  private createLightweightWavHeader(dataLength: number): Uint8Array {
    // 如果没有缓存，创建模板
    if (!this.cachedWavHeader) {
      const header = new ArrayBuffer(44);
      const view = new DataView(header);

      // RIFF header
      view.setUint32(0, 0x52494646, false); // "RIFF"
      view.setUint32(8, 0x57415645, false); // "WAVE"

      // fmt chunk (固定参数)
      view.setUint32(12, 0x666d7420, false); // "fmt "
      view.setUint32(16, 16, true); // chunk length
      view.setUint16(20, 1, true); // PCM format
      view.setUint16(22, 1, true); // mono
      view.setUint32(24, 24000, true); // 24kHz sample rate
      view.setUint32(28, 48000, true); // byte rate (24000 * 1 * 16 / 8)
      view.setUint16(32, 2, true); // block align (1 * 16 / 8)
      view.setUint16(34, 16, true); // 16 bits per sample

      // data chunk header
      view.setUint32(36, 0x64617461, false); // "data"

      this.cachedWavHeader = new Uint8Array(header);
    }

    // 复制缓存的头部并更新长度字段
    const header = new Uint8Array(this.cachedWavHeader);
    const view = new DataView(header.buffer);

    // 更新文件长度和数据长度
    view.setUint32(4, 36 + dataLength, true); // file length - 8
    view.setUint32(40, dataLength, true); // data length

    return header;
  }

  /**
   * 优化的Base64解码（减少性能开销）
   */
  private fastBase64Decode(base64: string): Uint8Array {
    // 使用浏览器原生的atob，然后批量转换
    const binaryString = atob(base64);
    const bytes = new Uint8Array(binaryString.length);

    // 批量处理，减少函数调用开销
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    return bytes;
  }

  /**
   * 检查是否为完整的WAV文件
   */
  private isCompleteWavFile(data: Uint8Array): boolean {
    if (data.length < 44) return false; // WAV头部至少44字节

    // 检查RIFF头部
    const riffCheck =
      data[0] === 0x52 && // R
      data[1] === 0x49 && // I
      data[2] === 0x46 && // F
      data[3] === 0x46; // F

    // 检查WAVE标识
    const waveCheck =
      data[8] === 0x57 && // W
      data[9] === 0x41 && // A
      data[10] === 0x56 && // V
      data[11] === 0x45; // E

    // 检查fmt chunk
    const fmtCheck =
      data[12] === 0x66 && // f
      data[13] === 0x6d && // m
      data[14] === 0x74 && // t
      data[15] === 0x20; // (space)

    return riffCheck && waveCheck && fmtCheck;
  }

  /**
   * 开始播放准备
   */
  public startLoading(): void {
    this.setState(PlayerState.LOADING);
    this.audioQueue = [];
    this.isPlaying = false;
    this.hasStartedPlaying = false;
    this.hasEmittedPlayStart = false;
    this.isReceivingComplete = false;
    this.currentPlayingIndex = 0;

    this.cleanupAudio();
  }

  /**
   * 停止播放
   */
  public stop(): void {
    this.isPlaying = false;
    this.audioQueue = [];
    this.currentPlayingIndex = 0;
    this.setState(PlayerState.IDLE);
    this.cleanupAudio();
  }

  /**
   * 暂停播放
   */
  public pause(): void {
    if (this.currentState === PlayerState.PLAYING) {
      // 暂停所有活跃的播放器
      this.playerPool.forEach(playerInstance => {
        if (playerInstance.isActive && !playerInstance.player.paused) {
          playerInstance.player.pause();
        }
      });
      this.setState(PlayerState.PAUSED);
    }
  }

  /**
   * 继续播放
   */
  public resume(): void {
    if (this.currentState === PlayerState.PAUSED) {
      // 恢复所有暂停的播放器
      this.playerPool.forEach(playerInstance => {
        if (playerInstance.isActive && playerInstance.player.paused) {
          playerInstance.player.play().catch((error: any) => {
            this.logger.error('恢复播放失败', {
              error,
              playerId: playerInstance.id,
              chunkId: playerInstance.chunkId,
            });
            this.setState(PlayerState.ERROR);
          });
        }
      });
    }
  }

  /**
   * 设置播放器状态
   */
  private setState(state: PlayerState): void {
    if (this.currentState === state) return;

    this.logger.debug(`播放器状态变化: ${this.currentState} -> ${state}`);
    this.currentState = state;
    this.eventBus.emit('tts:player-state-change', { state });
  }

  /**
   * 获取当前状态
   */
  public getState(): PlayerState {
    return this.currentState;
  }

  /**
   * 清理音频资源
   */
  private cleanupAudio(): void {
    // 清理所有播放器实例
    this.playerPool.forEach(playerInstance => {
      this.releasePlayer(playerInstance);
    });

    // 重置播放状态
    this.audioQueue = [];
    this.isPlaying = false;
    this.hasStartedPlaying = false;
    this.hasEmittedPlayStart = false;
    this.isReceivingComplete = false;
    this.currentPlayingIndex = 0;

    // 重置缓冲策略状态
    this.startTime = 0;
  }

  /**
   * 销毁播放器
   */
  public destroy(): void {
    this.stop();
    this.cleanupAudio();

    // 完全清理播放器池
    this.playerPool.forEach(playerInstance => {
      playerInstance.player.remove();
    });
    this.playerPool = [];

    this.logger.info('播放器已销毁');
  }
}
