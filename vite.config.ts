import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import checker from 'vite-plugin-checker';
import { existsSync, mkdirSync, copyFileSync, readdirSync, statSync } from 'fs';

// 自定义插件：保护静态资源文件
function protectStaticAssets() {
  return {
    name: 'protect-static-assets',
    buildStart() {
      console.log('🛡️  保护静态资源插件已启动');
    },
    buildEnd() {
      // 确保静态资源目录存在
      const distDir = resolve(__dirname, 'dist');
      const staticDir = resolve(distDir, 'static');
      const humanDir = resolve(staticDir, 'human');

      if (!existsSync(staticDir)) {
        mkdirSync(staticDir, { recursive: true });
        console.log('📁 创建 static 目录');
      }

      if (!existsSync(humanDir)) {
        mkdirSync(humanDir, { recursive: true });
        console.log('📁 创建 human 目录');
      }

      // 复制静态资源文件（如果源文件存在）
      const sourceStaticDir = resolve(__dirname, 'static');
      if (existsSync(sourceStaticDir)) {
        this.copyDirectory(sourceStaticDir, staticDir);
        console.log('✅ 静态资源文件已保护');
      }
    },
    copyDirectory(src, dest) {
      if (!existsSync(dest)) {
        mkdirSync(dest, { recursive: true });
      }

      const items = readdirSync(src);
      items.forEach(item => {
        const srcPath = resolve(src, item);
        const destPath = resolve(dest, item);

        if (statSync(srcPath).isDirectory()) {
          this.copyDirectory(srcPath, destPath);
        } else {
          copyFileSync(srcPath, destPath);
        }
      });
    }
  };
}

// 自定义插件：将CSS内嵌到JS中
function inlineCSS() {
  return {
    name: 'inline-css',
    generateBundle(options, bundle) {
      const cssFiles = Object.keys(bundle).filter(fileName => fileName.endsWith('.css'));
      const jsFiles = Object.keys(bundle).filter(fileName => fileName.endsWith('.js'));

      if (cssFiles.length > 0 && jsFiles.length > 0) {
        let cssContent = '';

        // 收集所有CSS内容
        cssFiles.forEach(fileName => {
          const cssFile = bundle[fileName];
          if (cssFile.type === 'asset' && typeof cssFile.source === 'string') {
            cssContent += cssFile.source + '\n';
          }
        });

        // 清理CSS内容，转义特殊字符
        cssContent = cssContent
          .replace(/\\/g, '\\\\')
          .replace(/`/g, '\\`')
          .replace(/\$/g, '\\$')
          .replace(/\r?\n/g, '\\n')
          .trim();

        // 将CSS内容注入到主JS文件中
        const mainJsFile = jsFiles.find(fileName => fileName.includes('web-service-sdk'));
        if (mainJsFile && bundle[mainJsFile].type === 'chunk') {
          const jsFile = bundle[mainJsFile];
          const cssInjectionCode = `
// 自动注入CSS样式到页面
(function() {
  if (typeof document !== 'undefined' && !document.getElementById('web-sdk-styles')) {
    const style = document.createElement('style');
    style.id = 'web-sdk-styles';
    style.textContent = \`${cssContent}\`;
    document.head.appendChild(style);
  }
})();

`;
          jsFile.code = cssInjectionCode + jsFile.code;
        }

        // 删除CSS文件，因为已经内嵌到JS中
        cssFiles.forEach(fileName => {
          delete bundle[fileName];
        });
      }
    }
  };
}

export default defineConfig({
  plugins: [
    react(),
    // 启用类型检查和ESLint检查
    checker({
      typescript: true,
      eslint: {
        lintCommand: 'eslint src/**/*.{ts,tsx}',
      },
    }),
    protectStaticAssets(), // 保护静态资源插件
    inlineCSS(), // 添加CSS内嵌插件
  ],
  css: {
    postcss: './postcss.config.js',
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    },
  },
  define: {
    'process.env.NODE_ENV': JSON.stringify('production'),
    'process.env': JSON.stringify({}),
    global: 'globalThis',
  },
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'WebSDK',
      formats: ['umd'], // 只生成UMD格式，便于直接在浏览器中使用
      fileName: () => 'web-service-sdk.js',
    },
    rollupOptions: {
      external: [],
      output: {
        globals: {},
        exports: 'named', // 避免default导出警告
        inlineDynamicImports: true, // 内联动态导入
        manualChunks: undefined, // 禁用代码分割
      },
    },
    minify: false, // 禁用压缩，便于调试
    sourcemap: false, // 不生成源码映射，减少文件数量
    cssCodeSplit: false, // 将所有CSS合并到一个文件
    assetsInlineLimit: 0, // 不内联资源，让我们的插件处理
    // 保护静态资源文件，确保构建时不被清理
    emptyOutDir: false, // 不清空输出目录，保护已存在的静态资源
  },
  server: {
    port: 3000,
    open: true,
  },
});
